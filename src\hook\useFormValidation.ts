import { useState, useCallback, useEffect } from 'react';
import { validateField, ValidationRule, ValidationResult } from '../utils/validation';

export interface UseFormValidationOptions {
  debounceMs?: number;
  validateOnChange?: boolean;
}

export interface FormValidationHook {
  errors: Record<string, string>;
  isValid: boolean;
  validateField: (fieldName: string, value: string, rules: ValidationRule) => void;
  validateForm: (formData: Record<string, string>, fieldRules: Record<string, ValidationRule>) => boolean;
  clearError: (fieldName: string) => void;
  clearAllErrors: () => void;
  setError: (fieldName: string, error: string) => void;
  hasErrors: boolean;
  getFieldError: (fieldName: string) => string | undefined;
}

/**
 * Custom hook for form validation with real-time feedback
 * 
 * @param options Configuration options for validation behavior
 * @returns Form validation utilities and state
 */
export const useFormValidation = (options: UseFormValidationOptions = {}): FormValidationHook => {
  const { debounceMs = 300, validateOnChange = true } = options;
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [debounceTimers, setDebounceTimers] = useState<Record<string, NodeJS.Timeout>>({});

  /**
   * Validates a single field with optional debouncing
   */
  const validateFieldWithDebounce = useCallback((
    fieldName: string, 
    value: string, 
    rules: ValidationRule
  ) => {
    // Clear existing timer for this field
    if (debounceTimers[fieldName]) {
      clearTimeout(debounceTimers[fieldName]);
    }

    // Set new timer
    const timer = setTimeout(() => {
      const result: ValidationResult = validateField(value, rules);
      
      setErrors(prev => {
        const newErrors = { ...prev };
        if (!result.isValid && result.error) {
          newErrors[fieldName] = result.error;
        } else {
          delete newErrors[fieldName];
        }
        return newErrors;
      });

      // Clean up timer
      setDebounceTimers(prev => {
        const newTimers = { ...prev };
        delete newTimers[fieldName];
        return newTimers;
      });
    }, validateOnChange ? debounceMs : 0);

    setDebounceTimers(prev => ({
      ...prev,
      [fieldName]: timer
    }));
  }, [debounceMs, validateOnChange, debounceTimers]);

  /**
   * Validates entire form and returns whether it's valid
   */
  const validateFormData = useCallback((
    formData: Record<string, string>, 
    fieldRules: Record<string, ValidationRule>
  ): boolean => {
    const newErrors: Record<string, string> = {};

    Object.entries(fieldRules).forEach(([fieldName, rules]) => {
      const value = formData[fieldName] || '';
      const result: ValidationResult = validateField(value, rules);
      
      if (!result.isValid && result.error) {
        newErrors[fieldName] = result.error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, []);

  /**
   * Clears error for a specific field
   */
  const clearError = useCallback((fieldName: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  /**
   * Clears all validation errors
   */
  const clearAllErrors = useCallback(() => {
    setErrors({});
    // Clear all debounce timers
    Object.values(debounceTimers).forEach(timer => clearTimeout(timer));
    setDebounceTimers({});
  }, [debounceTimers]);

  /**
   * Manually sets an error for a field
   */
  const setError = useCallback((fieldName: string, error: string) => {
    setErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));
  }, []);

  /**
   * Gets error message for a specific field
   */
  const getFieldError = useCallback((fieldName: string): string | undefined => {
    return errors[fieldName];
  }, [errors]);

  // Computed properties
  const hasErrors = Object.keys(errors).length > 0;
  const isValid = !hasErrors;

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      Object.values(debounceTimers).forEach(timer => clearTimeout(timer));
    };
  }, [debounceTimers]);

  return {
    errors,
    isValid,
    validateField: validateFieldWithDebounce,
    validateForm: validateFormData,
    clearError,
    clearAllErrors,
    setError,
    hasErrors,
    getFieldError
  };
};

/**
 * Predefined validation configurations for common forms
 */
export const ValidationConfigs = {
  accountSettings: {
    debounceMs: 300,
    validateOnChange: true
  },
  
  quickForm: {
    debounceMs: 100,
    validateOnChange: true
  },
  
  submitOnly: {
    debounceMs: 0,
    validateOnChange: false
  }
};

/**
 * Hook specifically for account settings validation
 */
export const useAccountSettingsValidation = () => {
  return useFormValidation(ValidationConfigs.accountSettings);
};

export default useFormValidation;
