import { useState, useEffect } from "react"
import { Accordion, AccordionItem, Card, CardBody, CardHeader, <PERSON><PERSON>, <PERSON><PERSON>, Chip, Avatar } from "@heroui/react"
import { Star, MessageCircle, ThumbsUp, Users, TrendingUp, Award } from "lucide-react"
import { getReviewsByServiceId, Review as BackendReview } from "../../../../../service/reviewService"
import { toast } from 'react-toastify'
import { useAuth } from 'react-oidc-context'
import ReviewForm, { ReviewData } from '../../../../components/ReviewForm/ReviewForm'
import { ReviewList } from "./review-list"
import type { Review, Reply } from "../service-review"
import ReviewImages from "../../../../components/ReviewImages/ReviewImages"
import { getUserDisplayName } from "../../../../../utils/userUtils"

interface ReviewAccordionProps {
  serviceId: string
  providerId?: string
  serviceName?: string
  onSubmitReview?: (reviewData: ReviewData) => Promise<void>
}

export default function ReviewAccordion({ serviceId, providerId, serviceName, onSubmitReview }: ReviewAccordionProps) {
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [isSubmittingReview, setIsSubmittingReview] = useState(false)
  const auth = useAuth()

  // Load reviews from backend
  const loadReviews = async () => {
    if (!serviceId) {
      setError('Service ID is required')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await getReviewsByServiceId(serviceId, { page: 1, limit: 50 })
      
      const mappedReviews = response.reviews.map((backendReview: BackendReview): Review => ({
        id: backendReview._id || backendReview.id || 'unknown',
        customerName: getUserDisplayName(backendReview.userName, backendReview.name, backendReview.userEmail),
        customerAvatar: backendReview.userProfileImage || backendReview.profileImage,
        rating: backendReview.rating || 0,
        title: backendReview.title || 'No title',
        content: backendReview.comment || backendReview.review || 'No content',
        date: new Date(backendReview.createdAt || backendReview.date || Date.now()).toLocaleDateString(),
        replies: [],
        // Map reviewerInfo from backend
        reviewerInfo: backendReview.reviewerInfo ? {
          name: backendReview.reviewerInfo.name,
          email: backendReview.reviewerInfo.email
        } : undefined,
        userName: backendReview.userName,
        name: backendReview.name,
        userEmail: backendReview.userEmail,
        email: backendReview.email,
        serviceRating: backendReview.serviceRating,
        qualityRating: backendReview.qualityRating,
        valueRating: backendReview.valueRating,
        communicationRating: backendReview.communicationRating,
        timelinessRating: backendReview.timelinessRating,
        imageNames: backendReview.imageNames || [],
        imageUrls: backendReview.imageUrls || [],
        likes: 0,
        dislikes: 0,
        userReaction: null,
        isVerified: backendReview.isVerified || false,
        helpfulCount: 0
      }))

      setReviews(mappedReviews);
    } catch (error) {
      console.error('Error loading reviews:', error)
      setError('Failed to load reviews. Please try again later.')
      toast.error('Failed to load reviews')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadReviews()
  }, [serviceId])

  // Handle new reply
  const handleNewReply = (reviewId: string, reply: Omit<Reply, "id">) => {
    const newReply: Reply = {
      ...reply,
      id: Date.now().toString(),
    }

    setReviews(
      reviews.map((review) =>
        review.id === reviewId ? { ...review, replies: [...review.replies, newReply] } : review,
      ),
    )
  }

  // Handle like/dislike for reviews
  const handleReviewLike = (reviewId: string, type: 'like' | 'dislike') => {
    setReviews((prevReviews) =>
      prevReviews.map((review) => {
        if (review.id === reviewId) {
          const currentReaction = review.userReaction
          let newLikes = review.likes
          let newDislikes = review.dislikes
          let newReaction: 'like' | 'dislike' | null = type

          if (currentReaction === 'like') {
            newLikes -= 1
          } else if (currentReaction === 'dislike') {
            newDislikes -= 1
          }

          if (currentReaction === type) {
            newReaction = null
          } else {
            if (type === 'like') {
              newLikes += 1
            } else {
              newDislikes += 1
            }
          }

          return {
            ...review,
            likes: newLikes,
            dislikes: newDislikes,
            userReaction: newReaction
          }
        }
        return review
      })
    )
    
    toast.success(`Review ${type}d successfully!`)
  }

  // Handle like/dislike for replies
  const handleReplyLike = (reviewId: string, replyId: string, type: 'like' | 'dislike') => {
    setReviews((prevReviews) =>
      prevReviews.map((review) => {
        if (review.id === reviewId) {
          const updatedReplies = review.replies.map((reply) => {
            if (reply.id === replyId) {
              const currentReaction = reply.userReaction
              let newLikes = reply.likes
              let newDislikes = reply.dislikes
              let newReaction: 'like' | 'dislike' | null = type

              if (currentReaction === 'like') {
                newLikes -= 1
              } else if (currentReaction === 'dislike') {
                newDislikes -= 1
              }

              if (currentReaction === type) {
                newReaction = null
              } else {
                if (type === 'like') {
                  newLikes += 1
                } else {
                  newDislikes += 1
                }
              }

              return {
                ...reply,
                likes: newLikes,
                dislikes: newDislikes,
                userReaction: newReaction
              }
            }
            return reply
          })

          return { ...review, replies: updatedReplies }
        }
        return review
      })
    )
    
    toast.success(`Reply ${type}d successfully!`)
  }

  // Handle review form submission
  const handleSubmitReview = async (reviewData: ReviewData) => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to submit a review')
      return
    }

    setIsSubmittingReview(true)
    try {
      if (onSubmitReview) {
        await onSubmitReview(reviewData)
      }
      setShowReviewForm(false)
      // Reload reviews to show the new one
      await loadReviews()
    } catch (error: any) {
      console.error('Error submitting review:', error)
      toast.error(error.message || 'Failed to submit review. Please try again.')
    } finally {
      setIsSubmittingReview(false)
    }
  }

  // Calculate statistics
  const averageRating = reviews.length > 0 
    ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1) 
    : "0.0"

  const totalReviews = reviews.length
  const verifiedReviews = reviews.filter(r => r.isVerified).length
  const recentReviews = reviews.filter(r => {
    const reviewDate = new Date(r.date)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    return reviewDate >= thirtyDaysAgo
  }).length

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spinner size="lg" color="primary" />
        <span className="ml-3 text-gray-600">Loading reviews...</span>
      </div>
    )
  }

  return (
    <div className="w-full">
      <Accordion selectionMode="multiple" defaultExpandedKeys={["summary", "reviews"]}>
        {/* Review Summary */}
        <AccordionItem 
          key="summary" 
          aria-label="Review Summary" 
          title={
            <div className="flex items-center gap-3">
              <Award className="w-5 h-5 text-yellow-500" />
              <span className="font-semibold">Review Summary</span>
              <Chip size="sm" color="primary" variant="flat">
                {totalReviews} Reviews
              </Chip>
            </div>
          }
        >
          <Card className="border-0 shadow-none">
            <CardBody className="px-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Overall Rating */}
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary mb-2">{averageRating}</div>
                  <div className="flex justify-center mb-2">
                    {[1, 2, 3, 4].map((star) => (
                      <Star
                        key={star}
                        className={`w-5 h-5 ${
                          star <= parseFloat(averageRating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-sm text-gray-600">Overall Rating</p>
                </div>

                {/* Total Reviews */}
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-600 mb-2">{totalReviews}</div>
                  <div className="flex justify-center mb-2">
                    <Users className="w-5 h-5 text-blue-600" />
                  </div>
                  <p className="text-sm text-gray-600">Total Reviews</p>
                </div>

                {/* Verified Reviews */}
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-600 mb-2">{verifiedReviews}</div>
                  <div className="flex justify-center mb-2">
                    <Award className="w-5 h-5 text-green-600" />
                  </div>
                  <p className="text-sm text-gray-600">Verified Reviews</p>
                </div>

                {/* Recent Reviews */}
                <div className="text-center">
                  <div className="text-4xl font-bold text-purple-600 mb-2">{recentReviews}</div>
                  <div className="flex justify-center mb-2">
                    <TrendingUp className="w-5 h-5 text-purple-600" />
                  </div>
                  <p className="text-sm text-gray-600">Recent (30 days)</p>
                </div>
              </div>

              {/* Rating Distribution */}
              {totalReviews > 0 && (
                <div className="mt-6">
                  <h4 className="font-semibold mb-4">Rating Distribution</h4>
                  <div className="space-y-2">
                    {[4, 3, 2, 1].map((rating) => {
                      const count = reviews.filter((r) => r.rating === rating).length
                      const percentage = (count / totalReviews) * 100
                      return (
                        <div key={rating} className="flex items-center gap-3">
                          <span className="w-8 text-sm">{rating} ⭐</span>
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-yellow-400 h-2 rounded-full transition-all"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                          <span className="w-12 text-sm text-gray-600">{count}</span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </AccordionItem>

        {/* Service Reviews */}
        <AccordionItem 
          key="reviews" 
          aria-label="Service Reviews" 
          title={
            <div className="flex items-center gap-3">
              <MessageCircle className="w-5 h-5 text-blue-500" />
              <span className="font-semibold">Customer Reviews</span>
              <Chip size="sm" color="success" variant="flat">
                {reviews.filter(r => r.replies.length > 0).length} with replies
              </Chip>
            </div>
          }
        >
          <div className="space-y-6">
            {error ? (
              <div className="text-center py-8">
                <p className="text-red-500 mb-4">{error}</p>
                <Button color="primary" variant="solid" onPress={loadReviews}>
                  Retry
                </Button>
              </div>
            ) : reviews.length > 0 ? (
              <ReviewList 
                reviews={reviews} 
                onReply={handleNewReply}
                onLike={handleReviewLike}
                onReplyLike={handleReplyLike}
              />
            ) : (
              <div className="text-center py-12">
                <MessageCircle className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No reviews yet</h3>
                <p className="text-gray-500">Be the first to share your experience with this service!</p>
              </div>
            )}
          </div>
        </AccordionItem>

        {/* Write Review */}
        <AccordionItem 
          key="write-review" 
          aria-label="Write Review" 
          title={
            <div className="flex items-center gap-3">
              <Star className="w-5 h-5 text-green-500" />
              <span className="font-semibold">Write a Review</span>
              <Chip size="sm" color="warning" variant="flat">
                Share your experience
              </Chip>
            </div>
          }
        >
          <Card className="border-0 shadow-none">
            <CardBody className="px-0">
              {auth?.isAuthenticated ? (
                <div>
                  <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">Share Your Experience</h4>
                    <p className="text-blue-700 text-sm">
                      Help others by sharing your honest review about this service. Your feedback is valuable!
                    </p>
                  </div>
                  
                  <Button
                    color="primary"
                    variant="solid"
                    size="lg"
                    onPress={() => setShowReviewForm(true)}
                    startContent={<Star className="w-5 h-5" />}
                    className="w-full"
                  >
                    Write Your Review
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-2">Login Required</h4>
                    <p className="text-gray-600 text-sm">
                      Please log in to your account to write a review for this service.
                    </p>
                  </div>
                  <Button color="primary" variant="solid">
                    Login to Write Review
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>
        </AccordionItem>
      </Accordion>

      {/* Review Form Modal */}
      <ReviewForm
        isOpen={showReviewForm}
        onClose={() => setShowReviewForm(false)}
        onSubmit={handleSubmitReview}
        providerId={providerId}
        serviceId={serviceId}
        serviceName={serviceName}
        isEdit={false}
      />
    </div>
  )
}
