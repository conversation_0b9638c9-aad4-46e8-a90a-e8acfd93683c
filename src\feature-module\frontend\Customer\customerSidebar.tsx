import { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Accordion, AccordionItem } from '@heroui/react';
import { useAuth } from 'react-oidc-context';
import {
  FaTh,
  FaMobileAlt,
  FaHeart,
  FaWallet,
  FaStar,
  FaComments,
  FaCog,
  FaSignOutAlt,
  FaShieldAlt,
  FaBell,
  FaPlug,
  FaTrash,
  FaUserCircle,
} from 'react-icons/fa';
import customer from '../../../assets/Img/Leonardo_Kino_XL_Professional_Man_2.jpg';
import { JSX } from 'react/jsx-runtime';
import { apiClient } from '../../../api';
import { ProfilePictureDisplay } from '../../components/ProfilePicture';
import {
  getProfilePictureUrlFromName,
  getUserProfileWithPicture,
  UserProfileData
} from '../../../service/profilePictureService';

const CustomerSidebar = () => {
  // Set default key (which accordion item is selected by default)
  const [defaultKey] = useState('settings');
  const [userName, setUserName] = useState('User');
  const [profileImage, setProfileImage] = useState('');
  const [profileImageUrl, setProfileImageUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState<UserProfileData | null>(null);
  const auth = useAuth();

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user) {
      return auth.user.profile.preferred_username ||
             auth.user.profile.sub ||
             auth.user.profile.email;
    }
    return null;
  }, [auth.user]);

  // Fetch user data from API using the profile service
  const fetchUserData = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch user data: No user ID or not authenticated');
      return;
    }

    try {
      setLoading(true);
      console.log(`Fetching user data for sidebar, ID: ${uid}`);

      // Use the profile service to get user data with proper profile picture handling
      const profileData = await getUserProfileWithPicture(uid);

      if (profileData) {
        console.log('User profile data fetched for sidebar:', profileData);
        setUserData(profileData);

        // Set user name from the profile data
        setUserName(profileData.name || profileData.email?.split('@')[0] || 'User');

        // Handle profile picture - prioritize profilePicture field
        const profileImageName = profileData.profileImage || profileData.profilePicture || '';
        if (profileImageName) {
          console.log('Found profile image name:', profileImageName);

          // If it's just an image name (not a full URL), convert it to full URL
          if (profileImageName && !profileImageName.startsWith('http')) {
            const fullUrl = getProfilePictureUrlFromName(profileImageName);
            setProfileImage(profileImageName); // Store the image name
            setProfileImageUrl(fullUrl); // Store the full URL
            console.log('Profile image URL generated:', fullUrl);
          } else {
            // If it's already a full URL, use it directly
            setProfileImage(profileImageName);
            setProfileImageUrl(profileImageName);
          }
        } else {
          // Clear profile image if none found
          setProfileImage('');
          setProfileImageUrl('');
        }
      }
    } catch (error) {
      console.error('Error fetching user data for sidebar:', error);
      // Fallback to direct API call if profile service fails
      try {
        const response = await apiClient.get(`api/v1/user/${uid}`);
        if (response.status === 200 && response.data) {
          const userData = response.data.user || response.data;
          setUserName(userData.name || userData.email?.split('@')[0] || 'User');

          // Handle profile picture from direct API response
          const profileImageData = userData.profilePicture || userData.profileImage || '';
          if (profileImageData && !profileImageData.startsWith('http')) {
            const fullUrl = getProfilePictureUrlFromName(profileImageData);
            setProfileImage(profileImageData);
            setProfileImageUrl(fullUrl);
          } else if (profileImageData) {
            setProfileImage(profileImageData);
            setProfileImageUrl(profileImageData);
          }
        }
      } catch (fallbackError) {
        console.error('Fallback API call also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  }, [auth.isAuthenticated, getUserId]);

  // Get user name from auth context and fetch user data
  useEffect(() => {
    if (!auth.isLoading) {
      if (auth.isAuthenticated && auth.user) {
        console.log('User profile in sidebar:', auth.user.profile);

        // Try to get user's name from different possible profile fields
        const name = auth.user.profile.name ||
                    auth.user.profile.given_name ||
                    auth.user.profile.preferred_username ||
                    auth.user.profile.email?.split('@')[0] ||
                    'User';

        console.log('Using name for sidebar:', name);
        setUserName(name);

        // Fetch user data including profile image
        fetchUserData();
      } else {
        console.log('User not authenticated in sidebar');
        setUserName('Guest User');
        setProfileImage('');
        setProfileImageUrl('');
        setUserData(null);
      }
    }
  }, [auth.isLoading, auth.isAuthenticated, auth.user, fetchUserData]);

  // Listen for profile picture updates from localStorage or custom events
  useEffect(() => {
    const handleProfilePictureUpdate = () => {
      console.log('Profile picture update detected, refreshing sidebar data');
      fetchUserData();
    };

    // Listen for custom profile picture update events
    window.addEventListener('profilePictureUpdated', handleProfilePictureUpdate);

    // Also listen for storage events (if profile updates are stored in localStorage)
    window.addEventListener('storage', (e) => {
      if (e.key === 'profilePictureUpdated') {
        handleProfilePictureUpdate();
      }
    });

    return () => {
      window.removeEventListener('profilePictureUpdated', handleProfilePictureUpdate);
      window.removeEventListener('storage', handleProfilePictureUpdate);
    };
  }, [fetchUserData]);

  return (
    <div className="bg-white shadow-lg rounded-lg min-h-screen h-full p-4 sticky top-6">
      {/* User Profile Section */}
      <div className="flex flex-col items-center bg-white rounded-lg p-4 border border-gray-300 shadow-md">
        <div className="w-24 h-24 sm:w-28 sm:h-28">
          {loading ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-200 animate-pulse rounded-full">
              <FaUserCircle className="text-gray-400 text-5xl" />
            </div>
          ) : (
            <ProfilePictureDisplay
              profileImage={profileImage}
              profileImageUrl={profileImageUrl}
              userName={userName}
              size="lg"
              className="w-full h-full"
              showBorder={true}
            />
          )}
        </div>
        {loading ? (
          <div className="mt-3 h-6 w-24 bg-gray-200 animate-pulse rounded"></div>
        ) : (
          <h6 className="text-lg font-semibold mt-3">{userName}</h6>
        )}
        <p className="text-sm text-gray-500">Member Since Sep 2021</p>
      </div>

      {/* Sidebar Menu */}
      <ul className="mt-4 space-y-2 w-full">
        <SidebarItem
          link="/customer/customer-dashboard"
          icon={<FaTh />}
          text="Dashboard"
        />
        <SidebarItem
          link="/customer/customer-booking"
          icon={<FaMobileAlt />}
          text="Bookings"
        />
        <SidebarItem
          link="/customer/customer-favourite"
          icon={<FaHeart />}
          text="Favorites"
        />
        {/* <SidebarItem
          link="/customer/customer-wallet"
          icon={<FaWallet />}
          text="Wallet"
        /> */}
        <SidebarItem
          link="/customer/customer-reviews"
          icon={<FaStar />}
          text="Reviews"
        />
        <SidebarItem
          link="/customer/customer-chat"
          icon={<FaComments />}
          text="Chat"
        />

        <Accordion defaultExpandedKeys={[defaultKey]}>
          <AccordionItem
            key="settings"
            aria-label="Settings"
            title={
              <>
                <FaCog className="inline mr-2" /> Settings
              </>
            }
          >
            <ul className="ml-4 space-y-2">
              <SidebarItem
                link="/customer/settings/customer-profile"
                text="Account Settings"
                icon={<FaCog />}
              />
              <SidebarItem
                link="/customer/settings/customer-security"
                text="Security Settings"
                icon={<FaShieldAlt />}
              />
              <SidebarItem
                link="/customer/settings/notification"
                text="Notifications"
                icon={<FaBell />}
              />
              <SidebarItem
                link="/customers/settings/connected-apps"
                text="Connected Apps"
                icon={<FaPlug />}
              />
              {/* <SidebarItem
                link="/customers/customer-favourite"
                text="Delete Account"
                modalTarget="#del-account"
                icon={<FaTrash />}
              /> */}
            </ul>
          </AccordionItem>
        </Accordion>

        <SidebarItem
          link="/authentication/login"
          icon={<FaSignOutAlt />}
          text="Logout"
        />
      </ul>
    </div>
  );
};

// Reusable Sidebar Item Component
interface SidebarItemProps {
  link: string;
  icon?: JSX.Element;
  text: string;
  active?: boolean;
  modalTarget?: string;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  link,
  icon,
  text,
  active = false,
  modalTarget,
}) => {
  return (
    <li>
      <Link
        className={`flex items-center px-3 py-2 rounded-lg transition-colors ${
          active
            ? 'text-primary font-semibold bg-blue-100'
            : 'text-gray-700 hover:text-primary hover:bg-gray-100'
        }`}
        to={link}
        {...(modalTarget
          ? { 'data-bs-toggle': 'modal', 'data-bs-target': modalTarget }
          : {})}
      >
        {icon && <span className="mr-2">{icon}</span>}
        <span>{text}</span>
      </Link>
    </li>
  );
};

export default CustomerSidebar;
