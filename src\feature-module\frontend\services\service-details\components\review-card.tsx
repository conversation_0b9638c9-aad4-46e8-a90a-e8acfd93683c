"use client"

import type React from "react"
import { useState } from "react"
import { Avatar, <PERSON>ton, Card, CardBody, CardHeader, Textarea, Input, Chip, Divider } from "@heroui/react"
import { Star, MessageCircle, Send, Image as ImageIcon, ThumbsUp, ThumbsDown, Heart, MoreHorizontal } from "lucide-react"
import type { Review, Reply } from "../service-review"
import ReviewImages from "../../../../components/ReviewImages/ReviewImages"
import { useAuth } from 'react-oidc-context'
import { toast } from 'react-toastify'

interface ReviewCardProps {
  review: Review
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
  onLike?: (reviewId: string, type: 'like' | 'dislike') => void
  onReplyLike?: (reviewId: string, replyId: string, type: 'like' | 'dislike') => void
}

// Component to display detailed ratings
const DetailedRatings = ({ review }: { review: Review }) => {
  const ratings = [
    { label: "Service Quality", value: review.serviceRating },
    { label: "Work Quality", value: review.qualityRating },
    { label: "Value for Money", value: review.valueRating },
    { label: "Communication", value: review.communicationRating },
    { label: "Timeliness", value: review.timelinessRating },
  ].filter(rating => rating.value !== undefined && rating.value !== null)

  if (ratings.length === 0) return null

  return (
    <div className="space-y-3">
      <h5 className="text-sm font-medium text-gray-700">Detailed Ratings</h5>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {ratings.map((rating) => (
          <div key={rating.label} className="flex items-center justify-between">
            <span className="text-sm text-gray-600">{rating.label}</span>
            <div className="flex items-center gap-1">
              <div className="flex">
                {[1, 2, 3, 4].map((star) => (
                  <Star
                    key={star}
                    className={`w-3 h-3 ${
                      star <= (rating.value || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500 ml-1">{rating.value}/4</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function ReviewCard({ review, onReply, onLike, onReplyLike }: ReviewCardProps) {
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [replyContent, setReplyContent] = useState("")
  const [replyAuthor, setReplyAuthor] = useState("")
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set())
  const auth = useAuth()

  const handleReplySubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!replyContent.trim() || !replyAuthor.trim()) return

    onReply(review.id, {
      authorName: replyAuthor.trim(),
      content: replyContent.trim(),
      date: new Date().toISOString().split("T")[0],
      isBusinessOwner: false,
      replies: [],
      likes: 0,
      dislikes: 0,
      userReaction: null,
    })

    setReplyContent("")
    setReplyAuthor("")
    setShowReplyForm(false)
  }

  // Handle like/dislike for review
  const handleReviewLike = (type: 'like' | 'dislike') => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to react to reviews')
      return
    }
    if (onLike) {
      onLike(review.id, type)
    }
  }

  // Handle like/dislike for reply
  const handleReplyReaction = (replyId: string, type: 'like' | 'dislike') => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to react to replies')
      return
    }
    if (onReplyLike) {
      onReplyLike(review.id, replyId, type)
    }
  }

  // Toggle reply expansion
  const toggleReplyExpansion = (replyId: string) => {
    const newExpanded = new Set(expandedReplies)
    if (newExpanded.has(replyId)) {
      newExpanded.delete(replyId)
    } else {
      newExpanded.add(replyId)
    }
    setExpandedReplies(newExpanded)
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-start gap-4">
          <Avatar
            src={review.customerAvatar || "/placeholder.svg"}
            name={review.customerName || 'Anonymous User'}
            size="md"
          />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold">{review.customerName || 'Anonymous User'}</h3>
              <span className="text-sm text-gray-500">{review.date}</span>
            </div>
            {/* Show reviewer email if available */}
            {(review.reviewerInfo?.email || review.userEmail || review.email) && (
              <div className="mb-2">
                <span className="text-xs text-gray-500">Email: </span>
                <span className="text-sm text-gray-600 font-medium">
                  {review.reviewerInfo?.email || review.userEmail || review.email}
                </span>
              </div>
            )}
            <div className="flex items-center gap-2 mb-2">
              <div className="flex">
                {[1, 2, 3, 4].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 ${
                      star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium">{review.rating}/4</span>
            </div>
            <h4 className="font-medium mb-2">{review.title}</h4>
          </div>
        </div>
      </CardHeader>
      <CardBody className="space-y-4">
        <p className="text-gray-600 leading-relaxed">{review.content}</p>

        {/* Detailed Ratings */}
        <DetailedRatings review={review} />

        {/* Review Images */}
        {review.imageNames && review.imageNames.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <ImageIcon className="w-4 h-4 text-gray-500" />
              <h5 className="text-sm font-medium text-gray-700">Photos</h5>
            </div>
            <ReviewImages
              imageNames={review.imageNames}
              imageUrls={review.imageUrls}
              maxDisplay={4}
              size="md"
              layout="grid-4"
              enablePreview={true}
              showCount={true}
              folderName="review-images"
            />
          </div>
        )}

        {review.replies.length > 0 && (
          <div className="space-y-4">
            <Divider />
            <div className="space-y-4">
              {review.replies.map((reply) => (
                <div key={reply.id} className="flex gap-3 pl-4 border-l-2 border-gray-200">
                  <Avatar
                    src={reply.authorAvatar || "/placeholder.svg"}
                    name={reply.authorName}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{reply.authorName}</span>
                      {reply.isBusinessOwner && (
                        <Chip size="sm" color="primary" variant="flat" className="text-xs">
                          Business
                        </Chip>
                      )}
                      <span className="text-xs text-gray-500">{reply.date}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{reply.content}</p>

                    {/* Reply Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => handleReplyReaction(reply.id, 'like')}
                        startContent={
                          <ThumbsUp
                            className={`w-3 h-3 ${reply.userReaction === 'like' ? 'fill-blue-500 text-blue-500' : 'text-gray-400'}`}
                          />
                        }
                        className={`text-xs ${reply.userReaction === 'like' ? 'text-blue-500' : 'text-gray-400'}`}
                      >
                        {reply.likes}
                      </Button>
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => handleReplyReaction(reply.id, 'dislike')}
                        startContent={
                          <ThumbsDown
                            className={`w-3 h-3 ${reply.userReaction === 'dislike' ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
                          />
                        }
                        className={`text-xs ${reply.userReaction === 'dislike' ? 'text-red-500' : 'text-gray-400'}`}
                      >
                        {reply.dislikes}
                      </Button>

                      {/* Nested Reply Button */}
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => toggleReplyExpansion(reply.id)}
                        startContent={<MessageCircle className="w-3 h-3" />}
                        className="text-xs text-gray-400"
                      >
                        Reply
                      </Button>
                    </div>

                    {/* Nested Replies */}
                    {reply.replies && reply.replies.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {reply.replies.slice(0, expandedReplies.has(reply.id) ? reply.replies.length : 2).map((nestedReply) => (
                          <div key={nestedReply.id} className="flex gap-2 pl-2 border-l border-gray-100">
                            <Avatar
                              src={nestedReply.authorAvatar || "/placeholder.svg"}
                              name={nestedReply.authorName}
                              size="sm"
                              className="w-6 h-6"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-xs">{nestedReply.authorName}</span>
                                {nestedReply.isBusinessOwner && (
                                  <Chip size="sm" color="primary" variant="flat" className="text-[10px] px-1 py-0">
                                    Business
                                  </Chip>
                                )}
                                <span className="text-[10px] text-gray-500">{nestedReply.date}</span>
                              </div>
                              <p className="text-xs text-gray-600">{nestedReply.content}</p>
                            </div>
                          </div>
                        ))}

                        {reply.replies.length > 2 && !expandedReplies.has(reply.id) && (
                          <Button
                            variant="light"
                            size="sm"
                            onPress={() => toggleReplyExpansion(reply.id)}
                            className="text-xs text-blue-500"
                          >
                            Show {reply.replies.length - 2} more replies
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-4">
            {/* Like/Dislike Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="light"
                size="sm"
                onPress={() => handleReviewLike('like')}
                startContent={
                  <ThumbsUp
                    className={`w-4 h-4 ${review.userReaction === 'like' ? 'fill-blue-500 text-blue-500' : 'text-gray-500'}`}
                  />
                }
                className={review.userReaction === 'like' ? 'text-blue-500' : 'text-gray-500'}
              >
                {review.likes}
              </Button>
              <Button
                variant="light"
                size="sm"
                onPress={() => handleReviewLike('dislike')}
                startContent={
                  <ThumbsDown
                    className={`w-4 h-4 ${review.userReaction === 'dislike' ? 'fill-red-500 text-red-500' : 'text-gray-500'}`}
                  />
                }
                className={review.userReaction === 'dislike' ? 'text-red-500' : 'text-gray-500'}
              >
                {review.dislikes}
              </Button>
            </div>

            {/* Reply Button */}
            <Button
              variant="light"
              size="sm"
              onPress={() => setShowReplyForm(!showReplyForm)}
              startContent={<MessageCircle className="w-4 h-4" />}
            >
              Reply ({review.replies.length})
            </Button>
          </div>

          {/* Review Metadata */}
          <div className="flex items-center gap-2">
            {review.isVerified && (
              <Chip size="sm" color="success" variant="flat" startContent="✓">
                Verified
              </Chip>
            )}
            {review.helpfulCount && review.helpfulCount > 0 && (
              <Chip size="sm" color="primary" variant="flat">
                {review.helpfulCount} found helpful
              </Chip>
            )}
          </div>
        </div>

        {showReplyForm && (
          <form onSubmit={handleReplySubmit} className="space-y-3 pt-2 border-t">
            <Input
              placeholder="Your name"
              value={replyAuthor}
              onValueChange={setReplyAuthor}
              isRequired
              variant="bordered"
            />
            <Textarea
              placeholder="Write your reply..."
              value={replyContent}
              onValueChange={setReplyContent}
              minRows={3}
              isRequired
              variant="bordered"
            />
            <div className="flex gap-2">
              <Button
                type="submit"
                size="sm"
                color="primary"
                startContent={<Send className="w-4 h-4" />}
              >
                Post Reply
              </Button>
              <Button
                type="button"
                variant="light"
                size="sm"
                onPress={() => setShowReplyForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </CardBody>
    </Card>
  )
}
