import React from 'react';
import { getImageUrlsFromNames, getImageUrlWithFallback, debugS3Configuration, getReliableImageUrl } from '../../frontend/Customer/aws/s3FileUpload';

interface ReviewImagesProps {
  imageNames?: string[];
  imageUrls?: string[];
  maxDisplay?: number;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showCount?: boolean;
  folderName?: string;
  layout?: 'row' | 'column' | 'grid-4';
  enablePreview?: boolean;
  onImageClick?: (images: string[], index: number) => void;
}

const ReviewImages: React.FC<ReviewImagesProps> = ({
  imageNames = [],
  imageUrls = [],
  maxDisplay = 3,
  size = 'md',
  className = '',
  showCount = true,
  folderName = 'review-images',
  layout = 'row',
  enablePreview = false,
  onImageClick
}) => {
  // State for image preview
  const [previewImage, setPreviewImage] = React.useState<string | null>(null);

  // State to track failed images
  const [failedImages, setFailedImages] = React.useState<Set<string>>(new Set());

  // State to track loading images
  const [loadingImages, setLoadingImages] = React.useState<Set<string>>(new Set());

  // Determine which images to display - prioritize imageNames over imageUrls
  const displayImages = React.useMemo(() => {
    if (imageNames.length > 0) {
      // Convert image names to URLs
      const urls = getImageUrlsFromNames(imageNames, folderName);
      console.log('ReviewImages: Converting image names to URLs:', {
        imageNames,
        folderName,
        generatedUrls: urls
      });
      return urls.filter(url => url && url.trim() !== ''); // Filter out empty URLs
    } else if (imageUrls.length > 0) {
      // Use provided URLs as fallback
      console.log('ReviewImages: Using provided URLs:', imageUrls);
      return imageUrls.filter(url => url && url.trim() !== ''); // Filter out empty URLs
    }
    console.log('ReviewImages: No images to display');
    return [];
  }, [imageNames, imageUrls, folderName]);

  // Get size classes
  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-32 h-32',
    xl: 'w-40 h-40'
  };

  const imageSize = sizeClasses[size];
  const totalImages = imageNames.length > 0 ? imageNames.length : imageUrls.length;

  // Don't render if no images
  if (displayImages.length === 0) {
    return null;
  }

  // Determine layout classes with appropriate spacing
  const layoutClasses = layout === 'column'
    ? 'flex flex-col gap-2'
    : layout === 'grid-4'
    ? 'grid grid-cols-4 gap-2'
    : 'flex flex-wrap gap-2';

  // Handle image click for preview
  const handleImageClick = (imageUrl: string, index: number) => {
    if (onImageClick) {
      // Use external callback if provided
      onImageClick(displayImages, index);
    } else if (enablePreview) {
      // Use internal preview if no external callback
      setPreviewImage(imageUrl);
    }
  };

  // Close preview
  const closePreview = () => {
    setPreviewImage(null);
  };

  return (
    <>
      <div className={`${layoutClasses} ${className}`}>
        {/* Display images up to maxDisplay limit */}
        {displayImages.slice(0, maxDisplay).map((imageUrl, idx) => (
          <div key={idx} className="relative group">
            <img
              src={imageUrl}
              alt={`Review image ${idx + 1}`}
              className={`${imageSize} object-cover rounded-lg border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 ${(enablePreview || onImageClick) ? 'cursor-pointer hover:opacity-90 hover:scale-105' : ''} ${loadingImages.has(imageUrl) ? 'opacity-50' : ''}`}
              onClick={() => handleImageClick(imageUrl, idx)}
              onLoadStart={() => {
                setLoadingImages(prev => new Set(prev).add(imageUrl));
              }}
              onLoad={() => {
                setLoadingImages(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(imageUrl);
                  return newSet;
                });
              }}
              onError={(e) => {
                // Enhanced error handling for broken images
                const target = e.target as HTMLImageElement;
                const errorDetails = {
                  imageUrl,
                  imageIndex: idx,
                  imageName: imageNames[idx] || 'N/A',
                  folderName,
                  originalSrc: target.src,
                  naturalWidth: target.naturalWidth,
                  naturalHeight: target.naturalHeight,
                  complete: target.complete,
                  timestamp: new Date().toISOString(),
                  userAgent: navigator.userAgent
                };

                console.error('ReviewImages: Failed to load image:', errorDetails);

                // Track failed image and clear loading state
                setFailedImages(prev => new Set(prev).add(imageUrl));
                setLoadingImages(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(imageUrl);
                  return newSet;
                });

                // Run S3 configuration debug on first error
                if (idx === 0) {
                  console.log('Running S3 configuration debug due to image load failure...');
                  debugS3Configuration();
                }

                // Prevent infinite error loops
                if (target.src.includes('data:image/svg+xml') || target.src.includes('placeholder') || target.src.includes('fallback-attempted') || target.src.includes('folder-fallback-attempted')) {
                  return;
                }

                // Try direct S3 fallback URL if we have an image name and haven't tried it yet
                const imageName = imageNames[idx];
                if (imageName && !target.src.includes('amazonaws.com')) {
                  const fallbackUrl = getImageUrlWithFallback(imageName, folderName);
                  if (fallbackUrl && fallbackUrl !== imageUrl) {
                    console.log('ReviewImages: Trying direct S3 fallback URL:', {
                      originalUrl: imageUrl,
                      fallbackUrl,
                      imageName,
                      reason: 'CDN_failure_detected'
                    });
                    target.src = fallbackUrl + '?fallback-attempted=true';
                    return;
                  }
                }

                // Try alternative folder names if the current one fails
                if (imageName && target.src.includes('amazonaws.com') && !target.src.includes('fallback-attempted')) {
                  const alternativeFolders = ['review-images', 'reviews'];
                  const currentFolder = folderName;
                  const alternativeFolder = alternativeFolders.find(folder => folder !== currentFolder);

                  if (alternativeFolder) {
                    const alternativeUrl = getImageUrlWithFallback(imageName, alternativeFolder);
                    if (alternativeUrl && alternativeUrl !== imageUrl) {
                      console.log('ReviewImages: Trying alternative folder:', {
                        originalUrl: imageUrl,
                        alternativeUrl,
                        imageName,
                        currentFolder,
                        alternativeFolder,
                        reason: 'folder_mismatch_fallback'
                      });
                      target.src = alternativeUrl + '?folder-fallback-attempted=true';
                      return;
                    }
                  }
                }

                // Try a generic placeholder image as final fallback
                const placeholderUrl = 'https://via.placeholder.com/150x150/f3f4f6/6b7280?text=Image+Not+Found';
                if (!target.src.includes('placeholder.com')) {
                  console.log('Trying placeholder image:', placeholderUrl);
                  target.src = placeholderUrl;
                  return;
                }

                // Create a simple SVG placeholder as data URL (last resort)
                const svgPlaceholder = `data:image/svg+xml;base64,${btoa(`
                  <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100" height="100" fill="#f3f4f6"/>
                    <text x="50" y="45" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">Image</text>
                    <text x="50" y="60" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">Not Found</text>
                  </svg>
                `)}`;

                target.src = svgPlaceholder;
              }}
            />

            {/* Click to view overlay */}
            {(enablePreview || onImageClick) && (
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center">
                <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs font-medium bg-black bg-opacity-50 px-2 py-1 rounded">
                  Click to view
                </div>
              </div>
            )}

            {/* Optional overlay for image source indicator */}
            <div className="absolute bottom-1 left-1 bg-black bg-opacity-60 text-white text-[10px] px-1.5 py-0.5 rounded font-medium">
              {imageNames.length > 0 ? 'S3' : 'URL'}
            </div>
          </div>
        ))}

        {/* Show count indicator if there are more images */}
        {showCount && totalImages > maxDisplay && (
          <div
            className={`${imageSize} bg-gray-100 hover:bg-gray-200 rounded-lg border border-gray-200 flex flex-col items-center justify-center text-xs text-gray-600 font-medium transition-all duration-300 cursor-pointer hover:shadow-md group`}
            onClick={() => onImageClick && onImageClick(displayImages, maxDisplay)}
            title={`View all ${totalImages} images`}
          >
            <div className="text-lg font-bold text-gray-700 group-hover:text-gray-900">
              +{totalImages - maxDisplay}
            </div>
            <div className="text-[10px] text-gray-500 group-hover:text-gray-700">
              more
            </div>
          </div>
        )}
      </div>

      {/* Image Preview Modal */}
      {enablePreview && previewImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={closePreview}
        >
          <div className="relative max-w-4xl max-h-4xl p-4">
            <img
              src={previewImage}
              alt="Preview"
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={closePreview}
              className="absolute top-2 right-2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-2 transition-all"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ReviewImages;

// Hook for managing review images
export const useReviewImages = (imageNames: string[], imageUrls: string[], folderName = 'review-images') => {
  const displayUrls = React.useMemo(() => {
    if (imageNames.length > 0) {
      return getImageUrlsFromNames(imageNames, folderName);
    }
    return imageUrls;
  }, [imageNames, imageUrls, folderName]);

  const hasImages = imageNames.length > 0 || imageUrls.length > 0;
  const totalCount = imageNames.length > 0 ? imageNames.length : imageUrls.length;

  return {
    displayUrls,
    hasImages,
    totalCount,
    imageNames,
    imageUrls
  };
};
