{"name": "new-gigmosac-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"@aws-sdk/client-s3": "^3.798.0", "@geoapify/geocoder-autocomplete": "^2.1.0", "@heroicons/react": "^2.2.0", "@heroui/react": "^2.6.14", "@pubuduth-aplicy/chat-ui": "^2.1.73", "@reduxjs/toolkit": "^2.5.1", "@tanstack/react-query": "^5.64.1", "axios": "^1.7.9", "clsx": "^2.1.1", "framer-motion": "^12.4.2", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "moment": "^2.30.1", "oidc-client-ts": "^3.2.0", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-calendar": "^5.1.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-icons": "^5.4.0", "react-lazy-load-image-component": "^1.6.3", "react-oidc-context": "^3.3.0", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-slick": "^0.30.3", "react-sticky-box": "^2.0.5", "react-toastify": "^11.0.5", "recharts": "^3.0.2", "redux": "^5.0.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.16", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-lazy-load-image-component": "^1.6.4", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "prettier": "3.3.3", "tailwindcss": "^3.4.14", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}