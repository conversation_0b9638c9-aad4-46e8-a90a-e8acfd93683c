/**
 * Utility functions for user-related operations
 */

/**
 * Get a user-friendly display name from various user data sources
 * Prioritizes actual names over email addresses and avoids anonymous placeholders
 */
export const getUserDisplayName = (
  userName?: string,
  name?: string,
  email?: string,
  authUser?: any,
  fallback: string = 'User'
): string => {
  // Priority order:
  // 1. userName (if provided and not generic)
  // 2. name (if provided and not generic)
  // 3. Auth user's name
  // 4. Auth user's given_name
  // 5. Auth user's preferred_username
  // 6. First part of email (before @)
  // 7. Auth user's email first part
  // 8. Fallback

  const genericNames = ['anonymous', 'user', 'guest', ''];
  
  // Check userName
  if (userName && !genericNames.includes(userName.toLowerCase().trim())) {
    return userName.trim();
  }
  
  // Check name
  if (name && !genericNames.includes(name.toLowerCase().trim())) {
    return name.trim();
  }
  
  // Check auth user's name
  if (authUser?.name && !genericNames.includes(authUser.name.toLowerCase().trim())) {
    return authUser.name.trim();
  }
  
  // Check auth user's given_name
  if (authUser?.given_name && !genericNames.includes(authUser.given_name.toLowerCase().trim())) {
    return authUser.given_name.trim();
  }
  
  // Check auth user's preferred_username
  if (authUser?.preferred_username && !genericNames.includes(authUser.preferred_username.toLowerCase().trim())) {
    return authUser.preferred_username.trim();
  }
  
  // Extract name from email
  if (email && email.includes('@')) {
    const emailName = email.split('@')[0];
    if (emailName && !genericNames.includes(emailName.toLowerCase().trim())) {
      return emailName.trim();
    }
  }
  
  // Extract name from auth user's email
  if (authUser?.email && authUser.email.includes('@')) {
    const emailName = authUser.email.split('@')[0];
    if (emailName && !genericNames.includes(emailName.toLowerCase().trim())) {
      return emailName.trim();
    }
  }
  
  return fallback;
};

/**
 * Get user's full name with proper formatting
 */
export const getUserFullName = (
  firstName?: string,
  lastName?: string,
  authUser?: any
): string => {
  // Try to get first and last name from provided parameters
  if (firstName && lastName) {
    return `${firstName.trim()} ${lastName.trim()}`;
  }
  
  // Try to get from auth user
  if (authUser?.given_name && authUser?.family_name) {
    return `${authUser.given_name.trim()} ${authUser.family_name.trim()}`;
  }
  
  // Fallback to single name
  return getUserDisplayName(firstName || lastName, undefined, undefined, authUser);
};

/**
 * Check if a user name appears to be a real name vs a generic placeholder
 */
export const isRealUserName = (name?: string): boolean => {
  if (!name || typeof name !== 'string') return false;
  
  const genericNames = [
    'anonymous', 'user', 'guest', 'admin', 'test', 'demo',
    'unknown', 'default', 'temp', 'temporary'
  ];
  
  const cleanName = name.toLowerCase().trim();
  return !genericNames.includes(cleanName) && cleanName.length > 0;
};

/**
 * Format user name for display with proper capitalization
 */
export const formatUserName = (name: string): string => {
  if (!name || typeof name !== 'string') return '';
  
  return name
    .trim()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Get user initials for avatar display
 */
export const getUserInitials = (
  userName?: string,
  name?: string,
  email?: string,
  authUser?: any
): string => {
  const displayName = getUserDisplayName(userName, name, email, authUser);
  
  if (!displayName || displayName === 'User') {
    return 'U';
  }
  
  const words = displayName.split(' ').filter(word => word.length > 0);
  
  if (words.length >= 2) {
    return `${words[0].charAt(0)}${words[1].charAt(0)}`.toUpperCase();
  } else if (words.length === 1) {
    return words[0].charAt(0).toUpperCase();
  }
  
  return 'U';
};
