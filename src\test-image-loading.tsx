import React from 'react';
import ReviewImages from './feature-module/components/ReviewImages/ReviewImages';

const TestImageLoading: React.FC = () => {
  // Test data with sample image names that might not exist
  const testImageNames = [
    'sample-review-1.jpg',
    'sample-plumbing-1.jpg', 
    'sample-plumbing-2.jpg',
    'non-existent-image.jpg'
  ];

  const testImageUrls = [
    'https://cdn.staging.gigmosaic.ca/review-images/sample-review-1.jpg',
    'https://via.placeholder.com/150x150/blue/white?text=Test+Image',
    'https://invalid-url-that-will-fail.com/image.jpg'
  ];

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Image Loading Test</h1>
      
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test 1: Image Names (S3/CDN URLs)</h2>
        <p className="text-gray-600">Testing image loading from image names with S3/CDN URL generation</p>
        <ReviewImages
          imageNames={testImageNames}
          maxDisplay={4}
          size="lg"
          showCount={true}
          folderName="review-images"
          enablePreview={true}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test 2: Direct URLs</h2>
        <p className="text-gray-600">Testing image loading from direct URLs (mix of valid and invalid)</p>
        <ReviewImages
          imageUrls={testImageUrls}
          maxDisplay={3}
          size="lg"
          showCount={true}
          enablePreview={true}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test 3: Mixed (Names + URLs)</h2>
        <p className="text-gray-600">Testing with both image names and URLs (names should take priority)</p>
        <ReviewImages
          imageNames={['sample-review-1.jpg']}
          imageUrls={testImageUrls}
          maxDisplay={4}
          size="md"
          showCount={true}
          enablePreview={true}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test 4: Empty Data</h2>
        <p className="text-gray-600">Testing with no images (should render nothing)</p>
        <ReviewImages
          imageNames={[]}
          imageUrls={[]}
          maxDisplay={3}
          size="md"
          showCount={true}
        />
        <p className="text-sm text-gray-500">^ Should be empty above this line</p>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Expected Behavior:</h3>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• Images that fail to load should show fallback placeholders</li>
          <li>• Loading states should be visible during image loading</li>
          <li>• Console should show detailed error information for failed images</li>
          <li>• S3 configuration debug should run on first image error</li>
          <li>• Fallback URLs should be attempted before showing placeholders</li>
        </ul>
      </div>
    </div>
  );
};

export default TestImageLoading;
